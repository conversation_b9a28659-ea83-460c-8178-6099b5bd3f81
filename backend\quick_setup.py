#!/usr/bin/env python
"""
Quick setup script for CoffeeMeet platform
This script will:
1. Set up a local SQLite database for testing
2. Create a test user
3. Start the Django server
"""
import os
import sys
import subprocess

def setup_local_database():
    """Switch to SQLite for local development"""
    print("🔧 Setting up local SQLite database...")
    
    # Create a local settings override
    local_settings = '''
# Local development settings override
import os
from .settings import *

# Override database to use SQLite for local development
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db_local.sqlite3',
    }
}

# Allow all origins for local development
CORS_ALLOW_ALL_ORIGINS = True

print("🔧 Using local SQLite database for development")
'''
    
    with open('coffee_meetings_platform/local_settings.py', 'w') as f:
        f.write(local_settings)
    
    # Set environment variable to use local settings
    os.environ['DJANGO_SETTINGS_MODULE'] = 'coffee_meetings_platform.local_settings'
    
    return True

def run_migrations():
    """Run Django migrations"""
    print("📦 Running database migrations...")
    try:
        result = subprocess.run([sys.executable, 'manage.py', 'migrate'], 
                              capture_output=True, text=True, check=True,
                              env={**os.environ, 'DJANGO_SETTINGS_MODULE': 'coffee_meetings_platform.local_settings'})
        print("✅ Migrations completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Migration failed: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False

def create_test_user():
    """Create a test user"""
    print("👤 Creating test user...")
    
    create_user_script = '''
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coffee_meetings_platform.local_settings')
django.setup()

from users.models import HRManager
from django.contrib.auth.hashers import make_password

email = '<EMAIL>'
password = 'password123'

# Delete existing user if exists
HRManager.objects.filter(email=email).delete()

# Create new user
user = HRManager.objects.create(
    name='Test User',
    email=email,
    password_hash=make_password(password),
    company_name='CoffeeMeet Demo Company'
)

print(f"✅ Created user: {user.name} ({user.email})")
print(f"🔑 Login credentials:")
print(f"   Email: {email}")
print(f"   Password: {password}")
'''
    
    try:
        result = subprocess.run([sys.executable, '-c', create_user_script], 
                              capture_output=True, text=True, check=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ User creation failed: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False

def start_server():
    """Start Django development server"""
    print("🚀 Starting Django development server...")
    print("📡 Server will be available at: http://localhost:8000")
    print("🔗 Frontend should be at: http://localhost:3001")
    print("\n🔑 LOGIN CREDENTIALS:")
    print("   Email: <EMAIL>")
    print("   Password: password123")
    print("\n⚡ Press Ctrl+C to stop the server\n")
    
    try:
        subprocess.run([sys.executable, 'manage.py', 'runserver', '8000'], 
                      env={**os.environ, 'DJANGO_SETTINGS_MODULE': 'coffee_meetings_platform.local_settings'})
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")

def main():
    print("🎯 CoffeeMeet Quick Setup")
    print("=" * 50)
    
    # Change to backend directory
    if not os.path.exists('manage.py'):
        print("❌ Please run this script from the backend directory")
        return
    
    # Setup local database
    if not setup_local_database():
        return
    
    # Run migrations
    if not run_migrations():
        return
    
    # Create test user
    if not create_test_user():
        return
    
    # Start server
    start_server()

if __name__ == '__main__':
    main()
