#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coffee_meetings_platform.settings')
django.setup()

from users.models import HRManager
from django.contrib.auth.hashers import make_password, check_password

def debug_login():
    email = '<EMAIL>'
    password = 'password123'
    name = 'Test User'
    company = 'CoffeeMeet Demo Company'

    print("🔍 DEBUG: Starting login debug...")
    
    try:
        # First, let's see all users in the database
        all_users = HRManager.objects.all()
        print(f"📊 Total users in database: {all_users.count()}")
        
        for user in all_users:
            print(f"   - {user.name} ({user.email})")
        
        # Check if our test user exists
        if HRManager.objects.filter(email=email).exists():
            user = HRManager.objects.get(email=email)
            print(f"✅ User found: {user.name} ({user.email})")
            print(f"🔐 Password hash: {user.password_hash[:50]}...")
            
            # Test password verification
            is_valid = check_password(password, user.password_hash)
            print(f"🔑 Password check result: {is_valid}")
            
            if not is_valid:
                print("❌ Password verification failed!")
                print("🔧 Let's recreate the user with correct password...")
                
                # Delete and recreate user
                user.delete()
                new_user = HRManager.objects.create(
                    name=name,
                    email=email,
                    password_hash=make_password(password),
                    company_name=company
                )
                print(f"✅ Recreated user: {new_user.name}")
                
                # Test again
                is_valid_new = check_password(password, new_user.password_hash)
                print(f"🔑 New password check result: {is_valid_new}")
            
        else:
            print(f"❌ User not found. Creating new user...")
            user = HRManager.objects.create(
                name=name,
                email=email,
                password_hash=make_password(password),
                company_name=company
            )
            print(f"✅ Created user: {user.name} ({user.email})")
            
            # Test password verification
            is_valid = check_password(password, user.password_hash)
            print(f"🔑 Password check result: {is_valid}")
        
        print(f"\n🎯 LOGIN CREDENTIALS:")
        print(f"   Email: {email}")
        print(f"   Password: {password}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_login()
