#!/usr/bin/env python
import os
import sys
import django
import subprocess

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coffee_meetings_platform.settings')

def main():
    print("🚀 Starting CoffeeMeet Backend Server...")
    print("📍 Working directory:", os.getcwd())
    
    try:
        # First run the debug script
        print("\n🔍 Running login debug...")
        subprocess.run([sys.executable, 'debug_login.py'], check=True)
        
        print("\n🌐 Starting Django development server...")
        print("📡 Server will be available at: http://localhost:8000")
        print("🔗 Frontend should be at: http://localhost:3001")
        print("\n⚡ Press Ctrl+C to stop the server\n")
        
        # Start Django server
        subprocess.run([sys.executable, 'manage.py', 'runserver', '8000'], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running command: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == '__main__':
    main()
