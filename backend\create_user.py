#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coffee_meetings_platform.settings')
django.setup()

from users.models import HRManager
from django.contrib.auth.hashers import make_password

def create_test_user():
    email = '<EMAIL>'
    password = 'password123'
    name = 'Test User'
    company = 'CoffeeMeet Demo Company'

    try:
        # Check if user already exists
        if HRManager.objects.filter(email=email).exists():
            user = HRManager.objects.get(email=email)
            print(f'✅ User already exists: {user.name} ({user.email})')
            print(f'🔑 Login with: {email} / {password}')
            return user
        
        # Create new user
        user = HRManager.objects.create(
            name=name,
            email=email,
            password_hash=make_password(password),
            company_name=company
        )
        print(f'✅ Created new user: {user.name} ({user.email})')
        print(f'🔑 Login with: {email} / {password}')
        return user
        
    except Exception as e:
        print(f'❌ Error creating user: {str(e)}')
        return None

if __name__ == '__main__':
    create_test_user()
